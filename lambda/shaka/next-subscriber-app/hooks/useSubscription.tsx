import { useQuery } from '@tanstack/react-query';
import { subscriptionKeys } from '@/query-keys/query-keys';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';

export function useSubscription() {
  const { apiClient, isAuthenticated } = useAuth();

  const {
    data: subscriptions,
    isPending: loadingSubscriptions,
    error: subscriptionsError
  } = useQuery({
    queryKey: subscriptionKeys.subscriptions,
    queryFn: () => subscriptionsService.getSubscriptions(apiClient),
    enabled: isAuthenticated
  });

  return {
    subscriptions,
    loadingSubscriptions,
    subscriptionsError
  };
}
