import { useMutation } from '@tanstack/react-query';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';
import { subscriptionKeys } from '@/query-keys/query-keys';
import { queryClient } from '@/providers/providers';

export function useResumePlan() {
  const { apiClient } = useAuth();

  const {
    mutate: resumePlan,
    isPending,
    error,
    isError,
    isSuccess
  } = useMutation({
    mutationFn: async (migrationId: number) =>
      subscriptionsService.resumePlan(apiClient, migrationId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: subscriptionKeys.subscriptions
      });
    }
  });

  return {
    resumePlan,
    isPending,
    error,
    isError,
    isSuccess
  };
}
