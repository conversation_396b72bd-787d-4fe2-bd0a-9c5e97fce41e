import { useMutation } from '@tanstack/react-query';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';
import { PlanNameFormData } from '@/schemas/schemas';
import { subscriptionKeys } from '@/query-keys/query-keys';
import { queryClient } from '@/providers/providers';

type LabelPlanData = {
  subscriptionId: number;
  label: PlanNameFormData;
};

export function useLabelPlan() {
  const { apiClient } = useAuth();

  const {
    mutate: labelPlan,
    // mutateAsync
    isPending,
    error,
    isError,
    isSuccess
  } = useMutation({
    mutationFn: async ({ subscriptionId, label }: LabelPlanData) =>
      subscriptionsService.labelPlan(apiClient, subscriptionId, label),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: subscriptionKeys.subscriptions
      });
    }
  });

  return {
    labelPlan,
    isPending,
    error,
    isError,
    isSuccess
  };
}
