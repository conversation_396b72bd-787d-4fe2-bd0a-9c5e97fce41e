import { useMutation } from '@tanstack/react-query';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';
import { subscriptionKeys } from '@/query-keys/query-keys';
import { queryClient } from '@/providers/providers';

export function useBuyExtraRoaming() {
  const { apiClient } = useAuth();

  const {
    mutate: buyExtraRoamingData,
    isPending,
    error,
    isError,
    isSuccess
  } = useMutation({
    mutationFn: async ({
      planId,
      roamingData
    }: {
      planId: number;
      roamingData: number;
    }) =>
      subscriptionsService.addExtraRoamingData(apiClient, planId, roamingData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: subscriptionKeys.subscriptions
      });
    }
  });

  return {
    buyExtraRoamingData,
    isPending,
    error,
    isError,
    isSuccess
  };
}
