import { useMutation } from '@tanstack/react-query';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';
import { subscriptionKeys } from '@/query-keys/query-keys';
import { queryClient } from '@/providers/providers';

export function useCancelPlan() {
  const { apiClient } = useAuth();

  const {
    mutate: cancelPlan,
    isPending,
    error,
    isError,
    isSuccess
  } = useMutation({
    mutationFn: async (subscriptionId: number) =>
      subscriptionsService.cancelPlan(apiClient, subscriptionId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: subscriptionKeys.subscriptions
      });
    }
  });

  return {
    cancelPlan,
    isPending,
    error,
    isError,
    isSuccess
  };
}
