import { useFocusError } from '@/hooks/useFocusError';
import React, { useState } from 'react';
import { Alert } from '@/components/alert/alert';
import { Divider } from '@/components/divider/divider';
import { trimWhiteSpace } from '@/utils/formatters';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/auth/hooks/use-auth';
// import { SignupProgressTrackedRoutes } from '@/context/signup-progress-context/signup-progress-tracked-routes';
import { useMutation } from '@tanstack/react-query';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { AxiosError } from 'axios';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import {
  DeliverooSignupFormData,
  validateDeliverooSignUpData
} from '@/schema/schema';
import useLocalStorage, { LocalKey } from '@/hooks/useLocalStorage';

interface SignupFormData {
  name: string;
  email: string;
  password: string;
}

interface SignupFormContainerProps extends React.PropsWithChildren {
  formData: SignupFormData;
  clearErrors: () => void;
  errorMap: Record<string, string | null>;
  handleSubmit: (data: DeliverooSignupFormData) => void;
}

interface FormFieldProps {
  id: string;
  label: string;
  type: string;
  placeholder: string;
  defaultValue?: string;
  error?: string | null;
  onChange: () => void;
  inputMode?: 'text' | 'email' | 'tel' | 'numeric';
  autoComplete?: string;
  pattern?: string;
  inputClassName?: string;
  value?: string;
  maxLength?: number;
  disabled?: boolean;
}

function FormField({
  id,
  label,
  type,
  placeholder,
  defaultValue,
  error,
  onChange,
  inputMode = 'text',
  autoComplete,
  pattern,
  maxLength,
  inputClassName = '',
  value,
  disabled
}: FormFieldProps) {
  return (
    <div className="w-full">
      <label htmlFor={id} className="font-semibold">
        {label}
      </label>
      <input
        id={id}
        name={id}
        type={type}
        inputMode={inputMode}
        placeholder={placeholder}
        className={`bg-secondary border-border text-placeholder placeholder:text-default placeholder-placeholder my-2 w-full rounded-[2px] border p-3 ${error ? 'border-2 border-red-500' : ''} ${inputClassName}`}
        defaultValue={defaultValue}
        onChange={onChange}
        autoComplete={autoComplete}
        pattern={pattern}
        value={value}
        maxLength={maxLength}
        disabled={disabled}
      />
      {error && (
        <Alert variant="error" message={error} align="left" className="mb-4" />
      )}
    </div>
  );
}

interface SignupFormProps {
  errors: Record<string, string | null>;
  onSubmit: (data: DeliverooSignupFormData) => void;
  className?: string;
  children?: React.ReactNode;
  formId: string;
}

export function SignupForm({
  errors = {},
  onSubmit,
  className = '',
  formId,
  children
}: SignupFormProps) {
  useFocusError(errors);
  const [storedSignupRiderId] = useLocalStorage(LocalKey.RIDER_ID, '');

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formValues = new FormData(form);
    const trimmedData: Record<string, string> = {};

    formValues.forEach((value, key) => {
      trimmedData[key] =
        typeof value === 'string' ? trimWhiteSpace(value) : String(value);
    });

    onSubmit({
      name: trimmedData.name,
      email: trimmedData.email,
      password: trimmedData.password,
      metadata: {
        rider_id: storedSignupRiderId,
        email: trimmedData.email
      }
    });
  };

  return (
    <form
      onSubmit={handleFormSubmit}
      className={`mt-6 grid gap-4 ${className || ''}`}
      id={formId}
    >
      {children}
    </form>
  );
}

export function SignupFormContainer({
  formData,
  errorMap,
  clearErrors,
  handleSubmit,
  children
}: SignupFormContainerProps) {
  const [storedSignupEmail] = useLocalStorage(LocalKey.RIDER_EMAIL, '');
  return (
    <>
      <SignupForm
        errors={errorMap}
        onSubmit={handleSubmit}
        formId="signupForm"
        className="grid grid-cols-1 lg:grid-cols-2"
      >
        <FormField
          id="name"
          label="Full Name"
          type="text"
          placeholder="Enter your full name"
          defaultValue={formData.name}
          error={errorMap.name}
          onChange={clearErrors}
          autoComplete="given-name"
          maxLength={50}
        />
        <FormField
          id="email"
          label="Email"
          type="text"
          inputMode="email"
          placeholder="Enter your email address "
          value={storedSignupEmail}
          disabled={Boolean(storedSignupEmail)}
          error={errorMap.email}
          onChange={clearErrors}
          autoComplete="email"
        />
        {Boolean(storedSignupEmail) && (
          <input type="hidden" name="email" value={storedSignupEmail} />
        )}
        <FormField
          id="password"
          label="Password"
          type="password"
          placeholder="At least 8 characters"
          defaultValue={formData.password}
          error={errorMap.password}
          onChange={clearErrors}
          autoComplete="new-password"
        />
      </SignupForm>
      <Divider className="my-2 mb-1" />
      {children}
    </>
  );
}

export function useSignUpFormSubmission() {
  // updateProgress?: (updates: Partial<SignupProgressTrackedRoutes>) => void
  const { signup } = useAuth<DeliverooSignupFormData>();
  const router = useRouter();
  const [errors, setErrors] = useState<string[]>([]);
  const [riderEmail] = useLocalStorage(LocalKey.RIDER_EMAIL, '');
  const [formData, setFormData] = useState<SignupFormData>({
    name: '',
    email: riderEmail,
    password: ''
  });

  const {
    mutateAsync: submitSignupForm,
    isPending,
    error: mutationError
  } = useMutation({
    mutationFn: async (payload: DeliverooSignupFormData) => signup(payload),
    onSuccess: () => {
      router.push(ROUTES_CONFIG['otp'].path);
      // updateProgress?.({ isRegistered: true });
      if (typeof window !== 'undefined') {
        localStorage.removeItem(LocalKey.RIDER_EMAIL);
        localStorage.removeItem(LocalKey.RIDER_ID);
      }
    },
    onError: (error: AxiosError) => setErrors(standariseNetworkError(error))
  });

  const handleSubmit = async (formData: DeliverooSignupFormData) => {
    setErrors([]);

    try {
      setFormData(formData);

      const result = validateDeliverooSignUpData(formData);
      if (result.success) {
        await submitSignupForm(formData);
        return result;
      } else {
        const formattedErrors = result.error?.format() || {};
        const flatErrors = flattenValidationErrors(formattedErrors);
        setErrors(flatErrors);
      }
    } catch {
      return;
    }
  };

  return {
    handleSubmit,
    isPending,
    errors,
    setErrors,
    mutationError,
    formData
  };
}
