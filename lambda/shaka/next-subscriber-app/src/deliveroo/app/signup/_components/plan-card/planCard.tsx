import React from 'react';
import Image from 'next/image';
import logo from '@/src/deliveroo/public/images/deliveroo-logo-white.png';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import {
  CloseIcon,
  DeliverooDecrementIcon,
  DeliverooIncrementIcon,
  DeliverooTickIcon
} from '@/icons/icons';
import { QuantityInput } from '@/components/quantity-buttons/quantity-buttons';
import { createTestId } from '@/utils/helpers';
import { Divider } from '@/components/divider/divider';
import { TermsConditions } from '@/components/terms-conditions/terms-conditions';
import { planOrderItems } from '@/src/uswitch/utils/constants';
import { plans } from '@/src/deliveroo/utils/constants';
import {
  MIN_MAIN_PLAN_QUANTITY,
  MAX_MAIN_PLAN_QUANTITY
} from '@/src/deliveroo/app/signup/_reducer/reducer';

interface PlanCardProps {
  planName: string;
  price: number;
  quantity: number;
  incrementMainPlan: () => void;
  decrementMainPlan: () => void;
  quantityAvailableToPurchase: number;
}

export function PlanCard({
  planName,
  price,
  quantity,
  incrementMainPlan,
  decrementMainPlan,
  quantityAvailableToPurchase
}: PlanCardProps) {
  console.log(quantityAvailableToPurchase < quantity);
  console.log(quantity);
  return (
    <article className="rounded-lg border-1 border-[#EDEDED]">
      <header className="bg-custom-gradient-header flex h-[66px] items-center justify-between rounded-t-lg p-4 lg:px-5 lg:py-3">
        <div>
          <h2 className="text-secondary">Unlimited eSIM</h2>
        </div>
        <div>
          <Image
            src={logo}
            priority
            alt="Deliveroo logo"
            width={120}
            height={32}
          />
        </div>
      </header>
      <div className="flex flex-wrap items-center justify-between">
        <ul className="space-y-4 p-4 lg:p-5">
          {plans.map((plan, index) => (
            <li className="flex items-center gap-2" key={index}>
              <DeliverooTickIcon />
              <p className="text-[18px] font-bold">{plan.feature}</p>
            </li>
          ))}
        </ul>

        <div className="mt-2 flex w-full flex-row items-end justify-center gap-2 lg:mt-0 lg:mr-10 lg:w-fit lg:flex-col lg:items-center lg:gap-0">
          <h3 className="text-2xl leading-none">£{price}</h3>
          <span className="text-[18px]">a month</span>
        </div>
      </div>

      <footer className="flex flex-col px-4 py-1 pb-4 text-center lg:px-5 lg:py-3">
        <span className="text-xxxs">Up to 3 plans per rider</span>

        <QuantityInput.Root
          className="my-6 flex items-center justify-center gap-4 lg:my-4"
          value={quantity}
          onIncrement={incrementMainPlan}
          onDecrement={decrementMainPlan}
          minQuantity={MIN_MAIN_PLAN_QUANTITY}
          maxQuantity={Math.min(
            MAX_MAIN_PLAN_QUANTITY,
            quantityAvailableToPurchase
          )}
        >
          <QuantityInput.Button
            className="border-primary flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border-3 bg-none text-[#43CCBC] disabled:cursor-not-allowed disabled:border-[#C8C8C8] disabled:text-[#C8C8C8]"
            data-testid={`decrement-${createTestId(planName)}`}
            action="decrement"
            aria-label={`Decrease quantity for ${planName}`}
          >
            <DeliverooIncrementIcon />
          </QuantityInput.Button>
          <QuantityInput.Display
            className="flex h-8 w-8 items-center justify-center rounded border border-[#EBEBEB] font-semibold"
            data-testid={`${createTestId(planName)}-quantity`}
          />
          <QuantityInput.Button
            className="border-primary flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border-3 bg-none text-[#43CCBC] disabled:cursor-not-allowed disabled:border-[#C8C8C8] disabled:text-[#C8C8C8]"
            data-testid={`increment-${createTestId(planName)}`}
            action="increment"
            aria-label={`Increase quantity for ${planName}`}
          >
            <DeliverooDecrementIcon />
          </QuantityInput.Button>
        </QuantityInput.Root>

        <FullDetailsButton
          className="deliverooLink text-[18px]"
          text="Full plan details"
        >
          {({ isOpen, setIsOpen }) =>
            isOpen && (
              <Modal onOpenChange={setIsOpen} open={isOpen}>
                <Modal.Overlay />
                <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                  <div className="mb-4 flex justify-end">
                    <Modal.Close>
                      <CloseIcon />
                    </Modal.Close>
                  </div>
                  <Modal.Title className="mb-6 text-xl font-semibold">
                    <div className="flex items-end justify-between">
                      <h1 className="leading-none lg:text-base">{planName}</h1>
                      <span>
                        <strong className="text-xs leading-none lg:text-base">
                          £{price}.00{' '}
                        </strong>
                        <span className="text-default text-xs leading-none font-normal lg:text-[18px]">
                          a month
                        </span>
                      </span>
                    </div>
                  </Modal.Title>
                  <Modal.Description>
                    <Divider />
                    <h2 className="mb-4">Your plan includes</h2>
                    <div key={planOrderItems[0].id} className="mb-4">
                      <ol className="space-y-2">
                        {Object.values(planOrderItems[0].description ?? {}).map(
                          (desc, index) => {
                            const isUnlimitedData =
                              desc.startsWith('Unlimited UK data');
                            const isEuropeanRoaming = desc.startsWith(
                              'European roaming up to 20GB per month'
                            );

                            return (
                              <li
                                className="flex items-center gap-2"
                                key={index}
                              >
                                <DeliverooTickIcon />
                                <div className="flex flex-col">
                                  <p className="text-[18px]">{desc}</p>

                                  {isUnlimitedData && (
                                    <span className="text-xxxs">
                                      Limited to 700GB per month
                                    </span>
                                  )}

                                  {isEuropeanRoaming && (
                                    <span className="text-xxxs">
                                      Limited to 10 days per trip
                                    </span>
                                  )}
                                </div>
                              </li>
                            );
                          }
                        )}
                      </ol>
                    </div>
                    <br />
                    <TermsConditions className="deliverooLink" />
                  </Modal.Description>
                </Modal.Content>
              </Modal>
            )
          }
        </FullDetailsButton>
      </footer>
    </article>
  );
}
