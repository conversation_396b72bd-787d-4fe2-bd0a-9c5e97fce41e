import React, { useState } from 'react';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useSwipe } from '@/hooks/useSwipe';
import Image from 'next/image';

const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON>',
    location: 'Birmingham',
    avatar: '/images/avatar.jpg',
    review:
      'It was super easy to get set up. Coverage in Birmingham is great so no complaints. Satisfied overall'
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'London',
    avatar: '/images/avatar.jpg',
    review:
      'Excellent service and fantastic coverage. The setup process was seamless and the customer support is top-notch.'
  },
  {
    id: 3,
    name: '<PERSON>',
    location: 'Manchester',
    avatar: '/images/avatar.jpg',
    review:
      "Great value for money. The network quality is consistently good and I've had zero issues since switching."
  }
];

const TestimonialCard = ({
  testimonial,
  isActive = false,
  isBlurred = false,
  isMobile = false
}) => (
  <div
    className={`carousel-card mx-auto flex h-full min-h-[348px] flex-col justify-between rounded-lg bg-white px-6 pt-10 pb-6 transition-all duration-300 lg:max-w-[100%] ${isActive ? 'scale-100' : 'scale-95'} ${isBlurred ? 'opacity-50 blur-sm' : ''} ${isMobile ? '' : 'flex-shrink-0'} w-80`}
  >
    <p className="text-xs font-bold text-pretty">{testimonial.review}</p>

    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <Image
          width={28}
          height={28}
          src={testimonial.avatar}
          alt="avatar"
          className="h-[28px] w-[28px] rounded-full object-cover"
        />
        <span className="font-medium text-[#D3D3D3]">{testimonial.name}</span>
      </div>
      <span className="text-[18px] font-semibold">{testimonial.location}</span>
    </div>
  </div>
);

export function Carousel() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const isMobile = useMediaQuery(1024);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const swipeHandlers = useSwipe(
    {
      onSwipedLeft: nextSlide,
      onSwipedRight: prevSlide
    },
    {
      threshold: 50,
      preventDefaultTouchmoveEvent: false,
      trackMouse: true
    }
  );

  if (isMobile) {
    return (
      <>
        <div
          className="relative px-3"
          {...swipeHandlers}
          style={{ touchAction: 'pan-y', userSelect: 'none', cursor: 'grab' }}
        >
          <button
            onClick={prevSlide}
            className="absolute top-1/2 left-1 z-10 -translate-y-1/2"
          >
            <ChevronLeft />
          </button>

          <button
            onClick={nextSlide}
            className="absolute top-1/2 right-1 z-10 -translate-y-1/2"
          >
            <ChevronRight />
          </button>

          <TestimonialCard
            testimonial={testimonials[currentSlide]}
            isActive={true}
            isMobile={isMobile}
          />
        </div>

        <div className="relative z-10 mt-6 flex justify-center gap-x-3">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`h-2 w-6 rounded-full transition-colors ${
                index === currentSlide ? 'w-8 bg-white' : 'bg-[#C2C2C2]'
              }`}
            />
          ))}
        </div>
      </>
    );
  }

  return (
    <>
      <div
        className="relative mx-auto max-w-6xl px-4"
        {...swipeHandlers}
        style={{ touchAction: 'pan-y', userSelect: 'none', cursor: 'grab' }}
      >
        <button
          onClick={prevSlide}
          className="absolute top-1/2 left-1 z-10 -translate-y-1/2 cursor-pointer"
        >
          <ChevronLeft />
        </button>

        <button
          onClick={nextSlide}
          className="absolute top-1/2 right-1 z-10 -translate-y-1/2 cursor-pointer"
        >
          <ChevronRight />
        </button>

        <div className="flex items-center justify-center space-x-8 px-16">
          <div className="hidden lg:block">
            <TestimonialCard
              testimonial={
                testimonials[
                  (currentSlide - 1 + testimonials.length) % testimonials.length
                ]
              }
              isBlurred={true}
            />
          </div>

          {/* Center card (active) */}
          <div className="relative">
            <TestimonialCard
              testimonial={testimonials[currentSlide]}
              isActive={true}
            />
          </div>

          <div className="hidden lg:block">
            <TestimonialCard
              testimonial={
                testimonials[(currentSlide + 1) % testimonials.length]
              }
              isBlurred={true}
            />
          </div>
        </div>
      </div>

      {/*<div className="mt-12 flex justify-center space-x-3">*/}
      {/*  {testimonials.map((_, index) => (*/}
      {/*    <button*/}
      {/*      key={index}*/}
      {/*      onClick={() => goToSlide(index)}*/}
      {/*      className={`h-2 w-6 rounded-full transition-colors ${*/}
      {/*        index === currentSlide ? 'w-8 bg-white' : 'bg-[#C2C2C2]'*/}
      {/*      }`}*/}
      {/*    />*/}
      {/*  ))}*/}
      {/*</div>*/}
    </>
  );
}

function ChevronLeft() {
  return (
    <svg
      width="14"
      height="28"
      viewBox="0 0 14 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.91895 2.16113C10.6512 1.28854 11.8343 1.25119 12.6064 2.05664C13.4236 2.90916 13.4712 4.37595 12.6992 5.2959L5.80078 13.5176L5.39648 13.999L5.80078 14.4814L12.6992 22.7021C13.471 23.6218 13.4234 25.0886 12.6064 25.9414C11.8344 26.7467 10.6512 26.7103 9.91895 25.8379L1.30078 15.5664C0.566969 14.6918 0.566941 13.3061 1.30078 12.4316L9.91895 2.16113Z"
        fill="black"
        stroke="none"
        strokeWidth="1.5"
      />
    </svg>
  );
}

function ChevronRight() {
  return (
    <svg
      width="14"
      height="28"
      viewBox="0 0 14 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.08105 25.8389C3.34882 26.7115 2.16565 26.7488 1.39355 25.9434C0.57635 25.0908 0.528807 23.624 1.30078 22.7041L8.19922 14.4824L8.60352 14.001L8.19922 13.5186L1.30078 5.29785C0.528988 4.37819 0.576634 2.91143 1.39355 2.05859C2.16559 1.25327 3.34877 1.28972 4.08105 2.16211L12.6992 12.4336C13.433 13.3082 13.4331 14.6939 12.6992 15.5684L4.08105 25.8389Z"
        fill="black"
        stroke="none"
        strokeWidth="1.5"
      />
    </svg>
  );
}
